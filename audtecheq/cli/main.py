"""
Main command line interface for AudioTechEquity pipeline.
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import List, Optional

from ..core.config import PipelineConfig
from ..core.exceptions import AudioTechEquityError
from .commands import (
    preprocess_command,
    vad_command,
    diarize_command,
    segment_command,
    asr_command,
    test_command,
    pipeline_command
)


def setup_logging(log_level: str = "INFO") -> None:
    """
    Setup logging configuration.

    Parameters
    ----------
    log_level : str
        Logging level (DEBUG, INFO, WARNING, ERROR)
    """
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def create_parser() -> argparse.ArgumentParser:
    """
    Create the main argument parser.

    Returns
    -------
    argparse.ArgumentParser
        Main argument parser
    """
    parser = argparse.ArgumentParser(
        prog='audtecheq',
        description='AudioTechEquity: Audio processing pipeline for speech analysis',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run complete pipeline
  audtecheq pipeline --input /path/to/audio --output /path/to/results

  # Run individual components
  audtecheq preprocess --input audio.wav --output preprocessed.wav
  audtecheq diarize --input audio.wav --output diarization_results/
  audtecheq asr --input segments/ --output transcriptions/

  # Test components
  audtecheq test diarization --input results/ --expected-speakers 2
  audtecheq test asr --input transcriptions/ --keywords keywords.txt
        """
    )

    # Global arguments
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='Path to configuration YAML file'
    )

    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )

    parser.add_argument(
        '--device',
        choices=['auto', 'cpu', 'cuda'],
        default='auto',
        help='Device to use for processing (default: auto)'
    )

    parser.add_argument(
        '--version', '-v',
        action='version',
        version='%(prog)s 1.0.0'
    )

    # Create subparsers for different commands
    subparsers = parser.add_subparsers(
        dest='command',
        help='Available commands',
        metavar='COMMAND'
    )

    # Add subcommands
    _add_preprocess_parser(subparsers)
    _add_vad_parser(subparsers)
    _add_silence_parser(subparsers)
    _add_diarize_parser(subparsers)
    _add_segment_parser(subparsers)
    _add_asr_parser(subparsers)
    _add_test_parser(subparsers)
    _add_pipeline_parser(subparsers)

    return parser


def _add_preprocess_parser(subparsers) -> None:
    """Add preprocessing subcommand parser."""
    parser = subparsers.add_parser(
        'preprocess',
        help='Preprocess audio files (resample, normalize, denoise)',
        description='Preprocess audio files through resampling, normalization, and denoising'
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='Input audio file or directory'
    )

    parser.add_argument(
        '--output', '-o',
        required=True,
        help='Output file or directory'
    )

    parser.add_argument(
        '--sample-rate',
        type=int,
        default=16000,
        help='Target sample rate in Hz (default: 16000)'
    )

    parser.add_argument(
        '--channels',
        type=int,
        choices=[1, 2],
        default=1,
        help='Target number of channels (default: 1)'
    )

    parser.add_argument(
        '--target-dbfs',
        type=float,
        default=-20.0,
        help='Target loudness in dBFS (default: -20.0)'
    )

    parser.add_argument(
        '--noise-reduction',
        type=float,
        default=0.8,
        help='Noise reduction strength 0.0-1.0 (default: 0.8)'
    )

    parser.add_argument(
        '--keep-intermediates',
        action='store_true',
        help='Keep intermediate processing files'
    )


def _add_vad_parser(subparsers) -> None:
    """Add VAD subcommand parser."""
    parser = subparsers.add_parser(
        'vad',
        help='Voice Activity Detection - remove silences',
        description='Apply Voice Activity Detection to remove long silences'
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='Input audio file or directory'
    )

    parser.add_argument(
        '--output', '-o',
        required=True,
        help='Output file or directory'
    )

    parser.add_argument(
        '--threshold',
        type=float,
        default=0.5,
        help='VAD threshold (default: 0.5)'
    )

    parser.add_argument(
        '--min-speech-duration',
        type=int,
        default=250,
        help='Minimum speech duration in ms (default: 250)'
    )

    parser.add_argument(
        '--min-silence-duration',
        type=int,
        default=100,
        help='Minimum silence duration in ms (default: 100)'
    )


def _add_silence_parser(subparsers) -> None:
    """Add silence processing subcommand parser."""
    parser = subparsers.add_parser(
        'discard-silences',
        help='Discard long silences from audio',
        description='Remove long silences from audio files while preserving speech boundaries'
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='Input audio file or directory'
    )

    parser.add_argument(
        '--output', '-o',
        required=True,
        help='Output file or directory'
    )

    parser.add_argument(
        '--vad-timestamps',
        help='Path to VAD timestamps JSON file (optional)'
    )

    parser.add_argument(
        '--max-silence-duration',
        type=float,
        default=2.0,
        help='Maximum silence duration to keep in seconds (default: 2.0)'
    )

    parser.add_argument(
        '--preserve-boundaries',
        action='store_true',
        default=True,
        help='Preserve small silences at segment boundaries (default: True)'
    )


def _add_diarize_parser(subparsers) -> None:
    """Add diarization subcommand parser."""
    parser = subparsers.add_parser(
        'diarize',
        help='Speaker diarization - identify speakers',
        description='Perform speaker diarization to identify and segment speakers'
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='Input audio file or directory'
    )

    parser.add_argument(
        '--output', '-o',
        required=True,
        help='Output directory for diarization results'
    )

    parser.add_argument(
        '--oracle-speakers',
        type=int,
        help='Known number of speakers (if available)'
    )

    parser.add_argument(
        '--max-speakers',
        type=int,
        default=8,
        help='Maximum number of speakers (default: 8)'
    )

    parser.add_argument(
        '--clustering-backend',
        choices=['spectral', 'kmeans', 'ahc'],
        default='spectral',
        help='Clustering algorithm (default: spectral)'
    )


def _add_segment_parser(subparsers) -> None:
    """Add segmentation subcommand parser."""
    parser = subparsers.add_parser(
        'segment',
        help='Extract and process speaker segments',
        description='Extract speaker segments and apply processing (padding, crossfade, chunking)'
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='Input audio file or directory'
    )

    parser.add_argument(
        '--diarization', '-d',
        required=True,
        help='Diarization results directory or CSV file'
    )

    parser.add_argument(
        '--output', '-o',
        required=True,
        help='Output directory for segments'
    )

    parser.add_argument(
        '--padding',
        type=float,
        default=0.2,
        help='Padding duration in seconds (default: 0.2)'
    )

    parser.add_argument(
        '--max-segment-length',
        type=float,
        default=15.0,
        help='Maximum segment length in seconds (default: 15.0)'
    )

    parser.add_argument(
        '--crossfade',
        action='store_true',
        help='Apply crossfade to segments'
    )

    parser.add_argument(
        '--extract-speaker',
        help='Extract only specific speaker (most active if not specified)'
    )


def _add_asr_parser(subparsers) -> None:
    """Add ASR subcommand parser."""
    parser = subparsers.add_parser(
        'asr',
        help='Automatic Speech Recognition',
        description='Perform speech recognition on audio segments'
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='Input audio file, directory, or segments'
    )

    parser.add_argument(
        '--output', '-o',
        required=True,
        help='Output directory for transcriptions'
    )

    parser.add_argument(
        '--model',
        default='turbo',
        help='Whisper model to use (default: turbo)'
    )

    parser.add_argument(
        '--language',
        default='en',
        help='Language code (default: en)'
    )

    parser.add_argument(
        '--format',
        choices=['txt', 'json', 'csv', 'docx'],
        default='txt',
        help='Output format (default: txt)'
    )

    parser.add_argument(
        '--with-timestamps',
        action='store_true',
        help='Include timestamps in output'
    )


def _add_test_parser(subparsers) -> None:
    """Add testing subcommand parser."""
    parser = subparsers.add_parser(
        'test',
        help='Test and evaluate pipeline components',
        description='Test and evaluate the quality of pipeline outputs'
    )

    test_subparsers = parser.add_subparsers(
        dest='test_component',
        help='Component to test'
    )

    # Diarization testing
    diar_parser = test_subparsers.add_parser(
        'diarization',
        help='Test diarization quality'
    )
    diar_parser.add_argument('--input', '-i', required=True, help='Diarization results directory')
    diar_parser.add_argument('--expected-speakers', type=int, help='Expected number of speakers')
    diar_parser.add_argument('--output', '-o', help='Output report file')

    # Enhanced diarization testing
    enhanced_diar_parser = test_subparsers.add_parser(
        'enhanced-diarization',
        help='Enhanced diarization testing with detailed metrics'
    )
    enhanced_diar_parser.add_argument('--input', '-i', required=True, help='Diarization results directory')
    enhanced_diar_parser.add_argument('--embeddings', help='Speaker embeddings file for cluster analysis')
    enhanced_diar_parser.add_argument('--expected-speakers', type=int, help='Expected number of speakers')
    enhanced_diar_parser.add_argument('--max-speakers-threshold', type=int, default=6, help='Maximum reasonable speakers')
    enhanced_diar_parser.add_argument('--output', '-o', help='Output report file')

    # ASR testing
    asr_parser = test_subparsers.add_parser(
        'asr',
        help='Test ASR quality and keyword recognition'
    )
    asr_parser.add_argument('--input', '-i', required=True, help='Transcription files or directory')
    asr_parser.add_argument('--keywords', help='Keywords file for testing')
    asr_parser.add_argument('--output', '-o', help='Output report file')


def _add_pipeline_parser(subparsers) -> None:
    """Add full pipeline subcommand parser."""
    parser = subparsers.add_parser(
        'pipeline',
        help='Run complete end-to-end pipeline',
        description='Run the complete AudioTechEquity pipeline from raw audio to final results'
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='Input audio file or directory'
    )

    parser.add_argument(
        '--output', '-o',
        required=True,
        help='Output directory for all results'
    )

    parser.add_argument(
        '--steps',
        nargs='+',
        choices=['preprocess', 'vad', 'discard-silences', 'diarize', 'segment', 'asr', 'test'],
        default=['preprocess', 'vad', 'discard-silences', 'diarize', 'segment', 'asr'],
        help='Pipeline steps to run (default: all except test)'
    )

    parser.add_argument(
        '--oracle-speakers',
        type=int,
        help='Known number of speakers (if available)'
    )

    parser.add_argument(
        '--keep-intermediates',
        action='store_true',
        help='Keep intermediate files from each step'
    )


def main_cli(args: Optional[List[str]] = None) -> int:
    """
    Main CLI entry point.

    Parameters
    ----------
    args : list, optional
        Command line arguments (uses sys.argv if None)

    Returns
    -------
    int
        Exit code (0 for success, non-zero for error)
    """
    parser = create_parser()
    parsed_args = parser.parse_args(args)

    # Setup logging
    setup_logging(parsed_args.log_level)
    logger = logging.getLogger(__name__)

    try:
        # Load configuration if provided
        config = None
        if parsed_args.config:
            config_path = Path(parsed_args.config)
            if config_path.exists():
                config = PipelineConfig.from_yaml(config_path)
                logger.info(f"Loaded configuration from {config_path}")
            else:
                logger.error(f"Configuration file not found: {config_path}")
                return 1
        else:
            config = PipelineConfig()

        # Override device if specified
        if hasattr(parsed_args, 'device') and parsed_args.device != 'auto':
            config.device = parsed_args.device

        # Route to appropriate command handler
        if parsed_args.command == 'preprocess':
            return preprocess_command(parsed_args, config)
        elif parsed_args.command == 'vad':
            return vad_command(parsed_args, config)
        elif parsed_args.command == 'discard-silences':
            from .commands import silence_command
            return silence_command(parsed_args, config)
        elif parsed_args.command == 'diarize':
            return diarize_command(parsed_args, config)
        elif parsed_args.command == 'segment':
            return segment_command(parsed_args, config)
        elif parsed_args.command == 'asr':
            return asr_command(parsed_args, config)
        elif parsed_args.command == 'test':
            return test_command(parsed_args, config)
        elif parsed_args.command == 'pipeline':
            return pipeline_command(parsed_args, config)
        else:
            parser.print_help()
            return 1

    except AudioTechEquityError as e:
        logger.error(f"AudioTechEquity error: {e}")
        return 1
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        if parsed_args.log_level == 'DEBUG':
            import traceback
            traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main_cli())
