"""
Command implementations for AudioTechEquity CLI.
"""

import logging
from pathlib import Path
from typing import Any

from ..core.config import PipelineConfig, PreprocessingConfig, VADConfig, DiarizationConfig
from ..preprocessing import AudioPreprocessor
from ..vad import VADProcessor, SilenceProcessor
from ..diarization import SpeakerDiarizer


def preprocess_command(args: Any, config: PipelineConfig) -> int:
    """
    Handle preprocessing command.

    Parameters
    ----------
    args : argparse.Namespace
        Parsed command line arguments
    config : PipelineConfig
        Pipeline configuration

    Returns
    -------
    int
        Exit code
    """
    logger = logging.getLogger(__name__)

    try:
        # Update config with command line arguments
        preprocess_config = PreprocessingConfig(
            target_sample_rate=args.sample_rate,
            target_channels=args.channels,
            target_dbfs=args.target_dbfs,
            noise_reduction_prop=args.noise_reduction
        )

        # Initialize processor
        processor = AudioPreprocessor(config=preprocess_config)

        input_path = Path(args.input)
        output_path = Path(args.output)

        if input_path.is_file():
            # Process single file
            logger.info(f"Processing single file: {input_path}")

            if args.keep_intermediates:
                result = processor.process_step_by_step(
                    input_path,
                    output_path.parent,
                    keep_intermediates=True
                )
            else:
                result = processor.process(input_path, output_path)

            logger.info(f"Preprocessing completed successfully")
            logger.info(f"Output: {result['output_file']}")

        elif input_path.is_dir():
            # Process directory
            logger.info(f"Processing directory: {input_path}")

            result = processor.process_batch(input_path, output_path)

            logger.info(f"Batch processing completed")
            logger.info(f"Processed files: {len(result['processed_files'])}")
            logger.info(f"Errors: {len(result['errors'])}")

            if result['errors']:
                logger.warning("Some files failed to process:")
                for error in result['errors']:
                    logger.warning(f"  {error['file']}: {error['error']}")

        else:
            logger.error(f"Input path does not exist: {input_path}")
            return 1

        return 0

    except Exception as e:
        logger.error(f"Preprocessing failed: {e}")
        return 1


def vad_command(args: Any, config: PipelineConfig) -> int:
    """
    Handle VAD command.

    Parameters
    ----------
    args : argparse.Namespace
        Parsed command line arguments
    config : PipelineConfig
        Pipeline configuration

    Returns
    -------
    int
        Exit code
    """
    logger = logging.getLogger(__name__)

    try:
        # Update config with command line arguments
        vad_config = VADConfig(
            threshold=args.threshold,
            min_speech_duration_ms=args.min_speech_duration,
            min_silence_duration_ms=args.min_silence_duration
        )

        # Initialize processor
        processor = VADProcessor(config=vad_config)

        input_path = Path(args.input)
        output_path = Path(args.output)

        if input_path.is_file():
            # Process single file
            logger.info(f"Processing VAD for: {input_path}")

            result = processor.process(input_path, output_path)

            logger.info(f"VAD processing completed")
            logger.info(f"Speech segments: {result['num_segments']}")
            logger.info(f"Original duration: {result['original_duration']:.2f}s")
            logger.info(f"Processed duration: {result['processed_duration']:.2f}s")

        elif input_path.is_dir():
            # Process directory
            logger.info(f"Processing VAD for directory: {input_path}")

            result = processor.process_batch(input_path, output_path)

            logger.info(f"Batch VAD processing completed")
            logger.info(f"Processed files: {len(result['processed_files'])}")

        else:
            logger.error(f"Input path does not exist: {input_path}")
            return 1

        return 0

    except Exception as e:
        logger.error(f"VAD processing failed: {e}")
        return 1


def silence_command(args: Any, config: PipelineConfig) -> int:
    """
    Handle silence processing command.

    Parameters
    ----------
    args : argparse.Namespace
        Parsed command line arguments
    config : PipelineConfig
        Pipeline configuration

    Returns
    -------
    int
        Exit code
    """
    logger = logging.getLogger(__name__)

    try:
        # Initialize processor
        processor = SilenceProcessor()

        input_path = Path(args.input)
        output_path = Path(args.output)
        vad_timestamps_path = Path(args.vad_timestamps) if args.vad_timestamps else None

        if input_path.is_file():
            # Process single file
            logger.info(f"Processing silence removal for: {input_path}")

            result = processor.process(
                input_path,
                output_path,
                vad_timestamps_path=vad_timestamps_path,
                max_silence_duration=args.max_silence_duration,
                preserve_boundaries=args.preserve_boundaries
            )

            logger.info(f"Silence processing completed")
            logger.info(f"Original duration: {result['original_duration']:.2f}s")
            logger.info(f"Processed duration: {result['processed_duration']:.2f}s")
            logger.info(f"Silence removed: {result['silence_removed']:.2f}s")
            logger.info(f"Compression ratio: {result['compression_ratio']:.2f}")

        elif input_path.is_dir():
            # Process directory
            logger.info(f"Processing silence removal for directory: {input_path}")

            # For directory processing, we'd need to implement batch processing
            # This is a simplified version
            processed_files = []
            errors = []

            for audio_file in input_path.rglob("*.wav"):
                try:
                    output_file = output_path / audio_file.relative_to(input_path)
                    output_file.parent.mkdir(parents=True, exist_ok=True)

                    result = processor.process(
                        audio_file,
                        output_file,
                        max_silence_duration=args.max_silence_duration,
                        preserve_boundaries=args.preserve_boundaries
                    )
                    processed_files.append(result)

                except Exception as e:
                    errors.append({"file": str(audio_file), "error": str(e)})
                    logger.error(f"Failed to process {audio_file}: {e}")

            logger.info(f"Batch silence processing completed")
            logger.info(f"Processed files: {len(processed_files)}")
            logger.info(f"Errors: {len(errors)}")

            if errors:
                logger.warning("Some files failed to process:")
                for error in errors:
                    logger.warning(f"  {error['file']}: {error['error']}")

        else:
            logger.error(f"Input path does not exist: {input_path}")
            return 1

        return 0

    except Exception as e:
        logger.error(f"Silence processing failed: {e}")
        return 1


def diarize_command(args: Any, config: PipelineConfig) -> int:
    """
    Handle diarization command.

    Parameters
    ----------
    args : argparse.Namespace
        Parsed command line arguments
    config : PipelineConfig
        Pipeline configuration

    Returns
    -------
    int
        Exit code
    """
    logger = logging.getLogger(__name__)

    try:
        # Update config with command line arguments
        diarization_config = DiarizationConfig(
            oracle_num_speakers=args.oracle_speakers,
            max_num_speakers=args.max_speakers,
            clustering_backend=args.clustering_backend
        )

        # Initialize processor
        processor = SpeakerDiarizer(config=diarization_config)

        input_path = Path(args.input)
        output_path = Path(args.output)

        if input_path.is_file():
            # Process single file
            logger.info(f"Processing diarization for: {input_path}")

            result = processor.process(
                input_path,
                output_path,
                oracle_num_speakers=args.oracle_speakers
            )

            logger.info(f"Diarization completed")
            logger.info(f"Speakers detected: {result['num_speakers']}")
            logger.info(f"Segments: {result['num_segments']}")
            logger.info(f"Results saved to: {result['output_dir']}")

        elif input_path.is_dir():
            # Process directory
            logger.info(f"Processing diarization for directory: {input_path}")

            result = processor.process_batch(input_path, output_path)

            logger.info(f"Batch diarization completed")
            logger.info(f"Processed files: {len(result['processed_files'])}")

        else:
            logger.error(f"Input path does not exist: {input_path}")
            return 1

        return 0

    except Exception as e:
        logger.error(f"Diarization failed: {e}")
        return 1


def segment_command(args: Any, config: PipelineConfig) -> int:
    """
    Handle segmentation command.

    Parameters
    ----------
    args : argparse.Namespace
        Parsed command line arguments
    config : PipelineConfig
        Pipeline configuration

    Returns
    -------
    int
        Exit code
    """
    logger = logging.getLogger(__name__)

    try:
        from ..segmentation import AudioSegmenter
        from ..core.config import SegmentationConfig

        # Update config with command line arguments
        segmentation_config = SegmentationConfig(
            padding_sec=args.padding,
            max_segment_length=args.max_segment_length,
            apply_crossfade=args.crossfade
        )

        # Initialize processor
        processor = AudioSegmenter(config=segmentation_config)

        input_path = Path(args.input)
        diarization_path = Path(args.diarization)
        output_path = Path(args.output)

        logger.info(f"Processing segmentation for: {input_path}")

        result = processor.process(
            input_path,
            output_path,
            diarization_path,
            extract_speaker=args.extract_speaker,
            apply_padding=True,
            apply_crossfade=args.crossfade
        )

        logger.info(f"Segmentation completed")
        logger.info(f"Target speaker: {result['target_speaker']}")
        logger.info(f"Extracted segments: {result['extracted_segments']}")
        logger.info(f"Results saved to: {result['output_dir']}")

        return 0

    except Exception as e:
        logger.error(f"Segmentation failed: {e}")
        return 1


def asr_command(args: Any, config: PipelineConfig) -> int:
    """
    Handle ASR command.

    Parameters
    ----------
    args : argparse.Namespace
        Parsed command line arguments
    config : PipelineConfig
        Pipeline configuration

    Returns
    -------
    int
        Exit code
    """
    logger = logging.getLogger(__name__)

    try:
        from ..asr import ASRProcessor
        from ..core.config import ASRConfig

        # Update config with command line arguments
        asr_config = ASRConfig(
            model_name=args.model,
            language=args.language
        )

        # Initialize processor
        processor = ASRProcessor(config=asr_config)

        input_path = Path(args.input)
        output_path = Path(args.output)

        logger.info(f"Processing ASR for: {input_path}")

        if input_path.is_file():
            # Process single file
            output_file = output_path / f"{input_path.stem}.{args.format}"
            result = processor.process(
                input_path,
                output_file,
                format=args.format,
                with_timestamps=args.with_timestamps
            )

            logger.info(f"ASR completed")
            logger.info(f"Transcription: {result['transcription'][:100]}...")
            logger.info(f"Language: {result['language']}")
            logger.info(f"Processing time: {result['processing_time']:.2f}s")

        else:
            # Process directory
            result = processor.process_batch(
                input_path,
                output_path,
                format=args.format,
                with_timestamps=args.with_timestamps
            )

            logger.info(f"Batch ASR completed")
            logger.info(f"Processed files: {len(result['processed_files'])}")
            logger.info(f"Errors: {len(result['errors'])}")

        return 0

    except Exception as e:
        logger.error(f"ASR failed: {e}")
        return 1


def test_command(args: Any, config: PipelineConfig) -> int:
    """
    Handle testing command.

    Parameters
    ----------
    args : argparse.Namespace
        Parsed command line arguments
    config : PipelineConfig
        Pipeline configuration

    Returns
    -------
    int
        Exit code
    """
    logger = logging.getLogger(__name__)

    try:
        if args.test_component == 'diarization':
            return _test_diarization(args, config)
        elif args.test_component == 'enhanced-diarization':
            return _test_enhanced_diarization(args, config)
        elif args.test_component == 'asr':
            return _test_asr(args, config)
        else:
            logger.error(f"Unknown test component: {args.test_component}")
            return 1

    except Exception as e:
        logger.error(f"Testing failed: {e}")
        return 1


def pipeline_command(args: Any, config: PipelineConfig) -> int:
    """
    Handle full pipeline command.

    Parameters
    ----------
    args : argparse.Namespace
        Parsed command line arguments
    config : PipelineConfig
        Pipeline configuration

    Returns
    -------
    int
        Exit code
    """
    logger = logging.getLogger(__name__)

    try:
        input_path = Path(args.input)
        output_path = Path(args.output)

        logger.info(f"Starting AudioTechEquity pipeline")
        logger.info(f"Input: {input_path}")
        logger.info(f"Output: {output_path}")
        logger.info(f"Steps: {args.steps}")

        # Create output directory structure
        output_path.mkdir(parents=True, exist_ok=True)

        current_input = input_path

        # Step 1: Preprocessing
        if 'preprocess' in args.steps:
            logger.info("Step 1/5: Preprocessing")
            preprocess_output = output_path / "01_preprocessed"

            preprocess_config = config.preprocessing
            processor = AudioPreprocessor(config=preprocess_config)

            if current_input.is_file():
                result = processor.process(
                    current_input,
                    preprocess_output / current_input.name
                )
                current_input = Path(result['output_file'])
            else:
                result = processor.process_batch(current_input, preprocess_output)
                current_input = preprocess_output

            logger.info("Preprocessing completed")

        # Step 2: VAD
        if 'vad' in args.steps:
            logger.info("Step 2/5: Voice Activity Detection")
            vad_output = output_path / "02_vad"

            vad_config = config.vad
            processor = VADProcessor(config=vad_config)

            if current_input.is_file():
                result = processor.process(
                    current_input,
                    vad_output / current_input.name
                )
                current_input = Path(result['output_file'])
            else:
                result = processor.process_batch(current_input, vad_output)
                current_input = vad_output

            logger.info("VAD completed")

        # Step 2.5: Discard Long Silences (optional)
        if 'discard-silences' in args.steps:
            logger.info("Step 2.5/6: Discard Long Silences")
            silence_output = output_path / "02_5_silence_processed"

            processor = SilenceProcessor()

            # Use VAD timestamps if available
            vad_timestamps_path = None
            if 'vad' in args.steps:
                vad_dir = output_path / "02_vad"
                # Look for VAD timestamps file
                for vad_file in vad_dir.rglob("*.json"):
                    if "timestamps" in vad_file.name.lower():
                        vad_timestamps_path = vad_file
                        break

            if current_input.is_file():
                result = processor.process(
                    current_input,
                    silence_output / current_input.name,
                    vad_timestamps_path=vad_timestamps_path,
                    max_silence_duration=2.0,
                    preserve_boundaries=True
                )
                current_input = Path(result['output_file'])
            else:
                # For directory processing
                silence_output.mkdir(exist_ok=True)
                for audio_file in current_input.rglob("*.wav"):
                    output_file = silence_output / audio_file.relative_to(current_input)
                    output_file.parent.mkdir(parents=True, exist_ok=True)

                    result = processor.process(
                        audio_file,
                        output_file,
                        max_silence_duration=2.0,
                        preserve_boundaries=True
                    )
                current_input = silence_output

            logger.info("Silence processing completed")

        # Step 3: Diarization
        if 'diarize' in args.steps:
            logger.info("Step 3/5: Speaker Diarization")
            diarization_output = output_path / "03_diarization"

            diarization_config = config.diarization
            if args.oracle_speakers:
                diarization_config.oracle_num_speakers = args.oracle_speakers

            processor = SpeakerDiarizer(config=diarization_config)

            if current_input.is_file():
                result = processor.process(
                    current_input,
                    diarization_output,
                    oracle_num_speakers=args.oracle_speakers
                )
            else:
                result = processor.process_batch(current_input, diarization_output)

            logger.info("Diarization completed")

        # Step 4: Segmentation
        if 'segment' in args.steps:
            logger.info("Step 4/6: Segmentation")
            segmentation_output = output_path / "04_segmentation"

            from ..segmentation import AudioSegmenter
            segmentation_config = config.segmentation
            processor = AudioSegmenter(config=segmentation_config)

            # Use diarization results from previous step
            diarization_input = output_path / "03_diarization"

            if current_input.is_file():
                result = processor.process(
                    current_input,
                    segmentation_output,
                    diarization_input,
                    extract_speaker=None  # Auto-select most active
                )
                current_input = segmentation_output
            else:
                # For directory processing, this would need batch implementation
                logger.warning("Directory segmentation not yet implemented")
                current_input = segmentation_output

            logger.info("Segmentation completed")

        # Step 5: ASR
        if 'asr' in args.steps:
            logger.info("Step 5/6: Automatic Speech Recognition")
            asr_output = output_path / "05_asr"

            from ..asr import ASRProcessor
            asr_config = config.asr
            processor = ASRProcessor(config=asr_config)

            if current_input.is_dir():
                result = processor.process_batch(
                    current_input,
                    asr_output,
                    format='json',
                    with_timestamps=True
                )
            else:
                result = processor.process(
                    current_input,
                    asr_output / f"{current_input.stem}.json",
                    format='json',
                    with_timestamps=True
                )

            current_input = asr_output
            logger.info("ASR completed")

        # Step 6: Testing (optional)
        if 'test' in args.steps:
            logger.info("Step 6/6: Testing and Evaluation")
            testing_output = output_path / "06_testing"
            testing_output.mkdir(exist_ok=True)

            # Test diarization if available
            diarization_dir = output_path / "03_diarization"
            if diarization_dir.exists():
                from ..testing import DiarizationTester
                diar_tester = DiarizationTester()
                diar_result = diar_tester.process(
                    diarization_dir,
                    testing_output / "diarization_test.json",
                    expected_speakers=args.oracle_speakers
                )
                logger.info("Diarization testing completed")

            # Test ASR if available
            asr_dir = output_path / "05_asr"
            if asr_dir.exists():
                from ..testing import ASRTester
                asr_tester = ASRTester()
                asr_result = asr_tester.process(
                    asr_dir,
                    testing_output / "asr_test.json"
                )
                logger.info("ASR testing completed")

            logger.info("Testing and evaluation completed")

        logger.info("Pipeline completed successfully")
        logger.info(f"Results saved to: {output_path}")

        return 0

    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        return 1


def _test_diarization(args: Any, config: PipelineConfig) -> int:
    """Test diarization results."""
    logger = logging.getLogger(__name__)

    try:
        from ..testing import DiarizationTester

        tester = DiarizationTester()

        input_path = Path(args.input)
        output_path = Path(args.output) if args.output else input_path.parent / "diarization_test_report.json"

        result = tester.process(
            input_path,
            output_path,
            expected_speakers=args.expected_speakers
        )

        logger.info(f"Diarization testing completed")
        logger.info(f"Report saved to: {result['output_file']}")

        return 0

    except Exception as e:
        logger.error(f"Diarization testing failed: {e}")
        return 1


def _test_enhanced_diarization(args: Any, config: PipelineConfig) -> int:
    """Test enhanced diarization results."""
    logger = logging.getLogger(__name__)

    try:
        from ..testing import EnhancedDiarizationTester

        tester = EnhancedDiarizationTester()

        input_path = Path(args.input)
        output_path = Path(args.output) if args.output else input_path.parent / "enhanced_diarization_test_report.json"
        embeddings_path = Path(args.embeddings) if args.embeddings else None

        result = tester.process(
            input_path,
            output_path,
            embeddings_path=embeddings_path,
            expected_speakers=args.expected_speakers,
            max_speakers_threshold=args.max_speakers_threshold,
            max_segment_duration=args.max_segment_duration
        )

        logger.info(f"Enhanced diarization testing completed")
        logger.info(f"Report saved to: {result['output_path']}")
        logger.info(f"Total flags raised: {result['summary']['total_flags_raised']}")
        logger.info(f"Overall quality: {result['summary']['overall_quality_assessment']}")

        return 0

    except Exception as e:
        logger.error(f"Enhanced diarization testing failed: {e}")
        return 1


def _test_asr(args: Any, config: PipelineConfig) -> int:
    """Test ASR results."""
    logger = logging.getLogger(__name__)

    try:
        from ..testing import ASRTester

        tester = ASRTester()

        input_path = Path(args.input)
        output_path = Path(args.output) if args.output else input_path.parent / "asr_test_report.json"

        result = tester.process(
            input_path,
            output_path,
            keywords_path=args.keywords
        )

        logger.info(f"ASR testing completed")
        logger.info(f"Report saved to: {result['output_file']}")

        return 0

    except Exception as e:
        logger.error(f"ASR testing failed: {e}")
        return 1
