#!/usr/bin/env python3
"""
Enhanced AudioTechEquity Pipeline Example

This example demonstrates the new components implemented according to the flow diagram:
1. Enhanced silence processing (discard long silences)
2. Enhanced diarization testing with specific metrics
3. Complete pipeline with all new components

Usage:
    python enhanced_pipeline_example.py --input audio.wav --output results/
"""

import argparse
import logging
from pathlib import Path

from audtecheq import (
    AudioPreprocessor, VADProcessor, SilenceProcessor, SpeakerDiarizer,
    EnhancedDiarizationTester
)
from audtecheq.core.config import (
    PreprocessingConfig, VADConfig, DiarizationConfig
)


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def run_enhanced_pipeline(input_path: Path, output_path: Path):
    """
    Run the enhanced AudioTechEquity pipeline with new components.
    
    This demonstrates the complete flow from the diagram:
    1. Preprocessing (resample, normalize, denoise)
    2. VAD (voice activity detection)
    3. Discard long silences (NEW)
    4. Diarization with spectral clustering
    5. Enhanced testing with detailed metrics (NEW)
    """
    logger = logging.getLogger(__name__)
    
    # Create output directory structure
    output_path.mkdir(parents=True, exist_ok=True)
    
    logger.info("🎵 Starting Enhanced AudioTechEquity Pipeline")
    logger.info(f"Input: {input_path}")
    logger.info(f"Output: {output_path}")
    
    # Step 1: Preprocessing
    logger.info("\n📊 Step 1: Preprocessing")
    logger.info("-" * 40)
    
    preprocess_config = PreprocessingConfig(
        target_sample_rate=16000,
        target_channels=1,
        target_dbfs=-20.0,
        noise_reduction_prop=0.8
    )
    
    preprocessor = AudioPreprocessor(config=preprocess_config)
    preprocess_output = output_path / "01_preprocessed.wav"
    
    preprocess_result = preprocessor.process(input_path, preprocess_output)
    logger.info(f"✅ Preprocessing completed: {preprocess_result['output_file']}")
    
    # Step 2: Voice Activity Detection
    logger.info("\n🎤 Step 2: Voice Activity Detection")
    logger.info("-" * 40)
    
    vad_config = VADConfig(
        threshold=0.5,
        min_speech_duration_ms=250,
        min_silence_duration_ms=100
    )
    
    vad_processor = VADProcessor(config=vad_config)
    vad_output = output_path / "02_vad_processed.wav"
    
    vad_result = vad_processor.process(preprocess_output, vad_output)
    logger.info(f"✅ VAD completed")
    logger.info(f"   Speech segments: {vad_result['num_segments']}")
    logger.info(f"   Compression ratio: {vad_result['processing_stats']['compression_ratio']:.2f}")
    
    # Step 3: Discard Long Silences (NEW FEATURE)
    logger.info("\n🔇 Step 3: Discard Long Silences")
    logger.info("-" * 40)
    
    silence_processor = SilenceProcessor()
    silence_output = output_path / "03_silence_processed.wav"
    
    # Use VAD timestamps for better silence detection
    vad_timestamps_path = output_path / "02_vad_processed_timestamps.json"
    
    silence_result = silence_processor.process(
        vad_output,
        silence_output,
        vad_timestamps_path=vad_timestamps_path if vad_timestamps_path.exists() else None,
        max_silence_duration=2.0,
        preserve_boundaries=True
    )
    
    logger.info(f"✅ Silence processing completed")
    logger.info(f"   Original duration: {silence_result['original_duration']:.2f}s")
    logger.info(f"   Processed duration: {silence_result['processed_duration']:.2f}s")
    logger.info(f"   Silence removed: {silence_result['silence_removed']:.2f}s")
    logger.info(f"   Long silences removed: {silence_result['processing_stats']['long_silences_removed']}")
    
    # Step 4: Speaker Diarization
    logger.info("\n👥 Step 4: Speaker Diarization")
    logger.info("-" * 40)
    
    diarization_config = DiarizationConfig(
        oracle_num_speakers=None,  # Let system determine
        max_num_speakers=4,
        clustering_backend="spectral"
    )
    
    diarizer = SpeakerDiarizer(config=diarization_config)
    diarization_output = output_path / "04_diarization"
    
    diarization_result = diarizer.process(silence_output, diarization_output)
    logger.info(f"✅ Diarization completed")
    logger.info(f"   Speakers detected: {diarization_result['num_speakers']}")
    logger.info(f"   Segments: {diarization_result['num_segments']}")
    
    # Step 5: Enhanced Diarization Testing (NEW FEATURE)
    logger.info("\n🧪 Step 5: Enhanced Diarization Testing")
    logger.info("-" * 40)
    
    enhanced_tester = EnhancedDiarizationTester()
    test_output = output_path / "05_enhanced_test_report.json"
    
    # Note: For cluster analysis, you would need speaker embeddings
    # This example shows the testing without embeddings
    test_result = enhanced_tester.process(
        diarization_output,
        test_output,
        embeddings_path=None,  # Would be path to speaker embeddings
        expected_speakers=2,   # Example: expecting 2 speakers
        max_speakers_threshold=6,
        max_segment_duration=30.0,
        enable_cluster_analysis=False  # Disabled without embeddings
    )
    
    logger.info(f"✅ Enhanced testing completed")
    logger.info(f"   Total flags raised: {test_result['summary']['total_flags_raised']}")
    logger.info(f"   Overall quality: {test_result['summary']['overall_quality_assessment']}")
    
    # Display specific test results
    if test_result['tests'].get('speaker_count_sanity'):
        speaker_test = test_result['tests']['speaker_count_sanity']
        logger.info(f"   Speaker count flags: {len(speaker_test['flags'])}")
    
    if test_result['tests'].get('turn_duration_analysis'):
        duration_test = test_result['tests']['turn_duration_analysis']
        logger.info(f"   Duration flags: {len(duration_test['flags'])}")
    
    # Display insights
    insights = test_result['summary'].get('insights', [])
    if insights:
        logger.info("\n💡 Insights:")
        for insight in insights:
            logger.info(f"   • {insight}")
    
    logger.info(f"\n🎉 Enhanced pipeline completed successfully!")
    logger.info(f"📁 All results saved to: {output_path}")
    
    return {
        'preprocess_result': preprocess_result,
        'vad_result': vad_result,
        'silence_result': silence_result,
        'diarization_result': diarization_result,
        'test_result': test_result
    }


def main():
    """Main function for the enhanced pipeline example."""
    parser = argparse.ArgumentParser(
        description="Enhanced AudioTechEquity Pipeline Example",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run enhanced pipeline on single file
  python enhanced_pipeline_example.py --input audio.wav --output results/

  # Run with custom parameters
  python enhanced_pipeline_example.py --input audio.wav --output results/ --expected-speakers 3
        """
    )
    
    parser.add_argument(
        '--input', '-i',
        type=Path,
        required=True,
        help='Input audio file'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=Path,
        required=True,
        help='Output directory for results'
    )
    
    parser.add_argument(
        '--expected-speakers',
        type=int,
        default=2,
        help='Expected number of speakers (default: 2)'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    
    # Validate input
    if not args.input.exists():
        print(f"Error: Input file does not exist: {args.input}")
        return 1
    
    if not args.input.suffix.lower() in ['.wav', '.mp3', '.flac', '.m4a']:
        print(f"Warning: Input file may not be a supported audio format: {args.input}")
    
    try:
        # Run the enhanced pipeline
        results = run_enhanced_pipeline(args.input, args.output)
        
        print(f"\n✨ Enhanced pipeline completed successfully!")
        print(f"📊 Check the results in: {args.output}")
        print(f"📋 Enhanced test report: {args.output / '05_enhanced_test_report.json'}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Pipeline failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
